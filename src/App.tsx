import type { Monaco } from '@monaco-editor/react'
import type { editor } from 'monaco-editor'
import Editor from '@monaco-editor/react'

function beforeMount(monaco: Monaco) {
  const content = `
  declare type Node = 'start' | 'LLM' | 'end'

  declare function $(node: Node): void
  `
  monaco.languages.typescript.typescriptDefaults.setExtraLibs([{
    content,
    filePath: 'global.d.ts',
  }])

  add
}

function onMount(editor: editor.IStandaloneCodeEditor, monaco: Monaco) {
  editor.onKeyUp((e) => {
    if (e.keyCode === monaco.KeyCode.Quote) {
      editor.trigger('', 'editor.action.triggerSuggest', '')
    }
  })
}

function App() {
  return (
    <Editor
      height="100vh"
      language="typescript"
      beforeMount={beforeMount}
      // onMount={onMount}
    />
  )
}

export default App
